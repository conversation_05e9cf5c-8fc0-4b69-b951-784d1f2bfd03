package com.jurassic.myhealth

import android.app.Application
import com.google.firebase.Firebase
import com.google.firebase.initialize
import com.jurassic.myhealth.di.initKoin
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger

internal class MyHealthApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        Firebase.initialize(this)

        initKoin {
            androidLogger()
            androidContext(this@MyHealthApplication)
        }
    }
}