package com.jurassic.myhealth.di

import com.jurassic.myhealth.app.di.mainModule
import com.jurassic.myhealth.home.data.di.homeDataModule
import com.jurassic.myhealth.home.domain.di.homeDomainModule
import com.jurassic.myhealth.home.presentation.di.homePresentationModule
import com.jurassic.myhealth.login.data.di.loginDataModule
import com.jurassic.myhealth.login.domain.di.loginDomainModule
import com.jurassic.myhealth.login.presentation.di.loginPresentationModule
import org.koin.core.context.startKoin
import org.koin.dsl.KoinAppDeclaration

fun initKoin(config: KoinAppDeclaration? = null) {

    startKoin {
        config?.invoke(this)
        modules(
            *loginPresentationModule,
            *homePresentationModule,
            providersModule,
            loginDomainModule,
            loginDataModule,
            homeDomainModule,
            homeDataModule,
            mainModule,
        )
    }
}