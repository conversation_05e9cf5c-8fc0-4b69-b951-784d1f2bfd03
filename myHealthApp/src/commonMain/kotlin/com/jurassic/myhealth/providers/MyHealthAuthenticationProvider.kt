package com.jurassic.myhealth.providers

import com.jurassic.myhealth.home.data.provider.AuthenticationProvider
import com.jurassic.myhealth.login.domain.gateway.LoginGateway

class MyHealthAuthenticationProvider(
    private val loginGateway: LoginGateway
) : AuthenticationProvider {

    override suspend fun getMyHealthToken(): Result<String> =
        loginGateway.getToken(false).map { it.token }
}