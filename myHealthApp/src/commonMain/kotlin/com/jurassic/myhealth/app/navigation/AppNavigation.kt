package com.jurassic.myhealth.app.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.jurassic.myhealth.app.composable.Navigation
import com.jurassic.myhealth.login.presentation.feature.signin.composable.SignInScreen
import com.jurassic.myhealth.login.presentation.feature.signup.composable.SignUpScreen
import com.jurassic.myhealth.home.presentation.feature.home.view.HomeScreen
import com.jurassic.myhealth.home.presentation.feature.provider.feature.add.composable.AddProviderScreen
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.composable.LoginProviderScreen

@Composable
fun AppNavigation(
    navController: NavHostController
) {

    NavHost(
        navController = navController,
        startDestination = Navigation.Routes.SIGN_IN
    ) {
        composable(
            route = Navigation.Routes.SIGN_IN
        ) {
            SignInScreen(navController)
        }

        composable(
            route = Navigation.Routes.SIGN_UP,
        ) {
            SignUpScreen(navController)
        }

        composable(
            route = Navigation.Routes.HOME,
        ) {
            HomeScreen()
        }

        composable(
            route = Navigation.Routes.ADD_PROVIDERS,
        ) {
            AddProviderScreen(navController)
        }

        composable(
            route = Navigation.Routes.LOGIN_PROVIDER,
        ) {
            LoginProviderScreen(navController)
        }
    }
}