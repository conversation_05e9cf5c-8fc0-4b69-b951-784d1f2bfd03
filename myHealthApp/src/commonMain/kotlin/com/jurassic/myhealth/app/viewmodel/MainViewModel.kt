package com.jurassic.myhealth.app.viewmodel

import com.jurassic.myhealth.login.domain.interactors.IsSignInUseCase
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.touchlab.kermit.Logger
import com.jurassic.myhealth.app.model.MainEffect
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

internal class MainViewModel(
    private val isSignInUseCase: IsSignInUseCase
) : ViewModel() {

    private val _effect: Channel<MainEffect> = Channel()
    val effect = _effect.receiveAsFlow()

    init {
        viewModelScope.launch {
            isSignInUseCase.execute()
                .onSuccess { isSignIn ->
                    if (isSignIn) {
                        _effect.send(MainEffect.NavigateToHome)
                    } else {
                        _effect.send(MainEffect.NavigateToSignIn)
                    }
                }
                .onFailure {
                    Logger.w(it) { "Failed to check isSignIn" }
                    _effect.send(MainEffect.NavigateToSignIn)
                }
        }
    }
}