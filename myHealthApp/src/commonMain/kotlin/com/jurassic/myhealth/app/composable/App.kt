package com.jurassic.myhealth.app.composable

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.navigation.compose.rememberNavController
import com.jurassic.myhealth.app.model.MainEffect
import com.jurassic.myhealth.app.navigation.AppNavigation
import com.jurassic.myhealth.app.viewmodel.MainViewModel
import com.jurassic.myhealth.composable.theme.MyHealthTheme
import com.jurassic.myhealth.login.presentation.feature.signin.composable.SIDE_EFFECTS_KEY
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel


@Composable
@Preview
fun App() {
    val viewModel = koinViewModel<MainViewModel>()

    val navController = rememberNavController()

    LaunchedEffect(SIDE_EFFECTS_KEY) {
        viewModel.effect.onEach { effect ->
            when (effect) {
                MainEffect.NavigateToHome -> navController.navigate(Navigation.Routes.ADD_PROVIDERS)
                MainEffect.NavigateToSignIn -> navController.navigate(Navigation.Routes.SIGN_IN)
            }
        }.collect()
    }

    MyHealthTheme {
        AppNavigation(navController)
    }
}

object Navigation {

    object Routes {
        const val SIGN_IN = "sign_in"
        const val SIGN_UP = "sign_up"
        const val HOME = "home"
        const val ADD_PROVIDERS = "add_providers"
        const val LOGIN_PROVIDER = "login_provider"
    }
}