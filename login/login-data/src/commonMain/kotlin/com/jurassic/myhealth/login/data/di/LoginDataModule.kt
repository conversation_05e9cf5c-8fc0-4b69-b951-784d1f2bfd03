package com.jurassic.myhealth.login.data.di

import com.jurassic.myhealth.login.data.gateway.FirebaseLoginGateway
import com.jurassic.myhealth.login.domain.gateway.LoginGateway
import dev.gitlive.firebase.Firebase
import dev.gitlive.firebase.auth.FirebaseAuth
import dev.gitlive.firebase.auth.auth
import org.koin.dsl.module

val loginDataModule = module {

    factory<FirebaseAuth> {
        Firebase.auth
    }

    factory<LoginGateway> {
        FirebaseLoginGateway(get())
    }
}