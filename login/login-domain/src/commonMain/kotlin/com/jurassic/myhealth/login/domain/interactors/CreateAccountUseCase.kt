package com.jurassic.myhealth.login.domain.interactors

import com.jurassic.myhealth.login.domain.gateway.LoginGateway
import com.jurassic.myhealth.login.domain.specyfication.CreateAccountSpecification
import com.jurassic.myhealth.login.domain.util.then

class CreateAccountUseCase(
    private val createAccountSpecification: CreateAccountSpecification,
    private val loginGateway: LoginGateway,
) {

    suspend fun execute(email: String, password: String, repeatPassword: String): Result<Unit> =
        runCatching {
            createAccountSpecification.isSatisfyBy(email, password, repeatPassword)
        }.then {
            loginGateway.createAccount(email, password)
        }
}