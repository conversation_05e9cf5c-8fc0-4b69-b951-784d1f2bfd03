package com.jurassic.myhealth.login.domain.di

import com.jurassic.myhealth.login.domain.interactors.CreateAccountUseCase
import com.jurassic.myhealth.login.domain.interactors.IsSignInUseCase
import com.jurassic.myhealth.login.domain.interactors.LoginUseCase
import com.jurassic.myhealth.login.domain.specyfication.CreateAccountSpecification
import org.koin.core.module.dsl.factoryOf
import org.koin.dsl.module

val loginDomainModule = module {

    factoryOf(::LoginUseCase)

    factoryOf(::IsSignInUseCase)

    factoryOf(::CreateAccountUseCase)

    factoryOf(::CreateAccountSpecification)
}