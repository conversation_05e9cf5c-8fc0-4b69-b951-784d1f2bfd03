package com.jurassic.myhealth.login.domain.specyfication

import com.jurassic.myhealth.login.domain.model.InvalidEmailException
import com.jurassic.myhealth.login.domain.model.PasswordNotMatchException

class CreateAccountSpecification {

    fun isSatisfyBy(email: String, password: String, repeatPassword: String): Bo<PERSON>an {
        if (!email.isValidEmail()) {
            throw InvalidEmailException
        }

//        if (!password.isValidPassword()) {
//            throw InvalidPasswordException
//        }

        if (!password.passwordMatches(repeatPassword)) {
            throw PasswordNotMatchException
        }

        return true
    }

    private fun String.isValidEmail(): Boolean =
        isNotBlank() && matches(EMAIL_ADDRESS_REGEX)

    private fun String.passwordMatches(repeated: String): Boolean =
        this == repeated

    private fun String.isValidPassword(): Boolean =
        isNotBlank() &&
                length >= MIN_PASS_LENGTH &&
                PASS_PATTERN.matches(this)

    private companion object {
        const val MIN_PASS_LENGTH = 6

        val PASS_PATTERN = Regex("^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=\\S+$).{4,}$")

        val EMAIL_ADDRESS_REGEX = Regex(
            "[a-zA-Z0-9\\+\\.\\_\\%\\-\\+]{1,256}" +
                    "\\@" +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
                    "(" +
                    "\\." +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
                    ")+"
        )
    }
}