package com.jurassic.myhealth.login.presentation.feature.signin.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jurassic.myhealth.login.domain.interactors.LoginUseCase
import com.jurassic.myhealth.login.presentation.feature.signin.model.SignInContract
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

internal class SignInViewModel(
    private val loginUseCase: LoginUseCase
) : ViewModel() {

    private val _effect: Channel<SignInContract.Effect> = Channel()
    val effect = _effect.receiveAsFlow()

    var uiState = mutableStateOf(SignInContract.UiState())
        private set

    fun onEmailChange(email: String) {
        uiState.value = uiState.value.copy(email = email)
    }

    fun onPasswordChange(password: String) {
        uiState.value = uiState.value.copy(password = password)
    }

    fun onSignInClick() {
        viewModelScope.launch {
            loginUseCase.execute(uiState.value.email, uiState.value.password)
                .onSuccess {
                    _effect.send(SignInContract.Effect.NavigateToAddProviders)
                }
                .onFailure {
//                    TODO handle error
                }
        }
    }

    fun onForgotPasswordClick() {
        TODO("Not yet implemented")
    }

    fun onCreateAccountClick() {
        viewModelScope.launch {
            _effect.send(SignInContract.Effect.NavigateToSignUp)
        }
    }
}