package com.jurassic.myhealth.login.presentation.feature.signup.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jurassic.myhealth.login.domain.interactors.CreateAccountUseCase
import com.jurassic.myhealth.login.presentation.feature.signup.model.SignUpContract
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class SignUpViewModel(
    private val createAccountUseCase: CreateAccountUseCase
) : ViewModel() {

    var uiState = mutableStateOf(SignUpContract.UiState())
        private set

    private val _effect: Channel<SignUpContract.Effect> = Channel()
    val effect = _effect.receiveAsFlow()

    fun onEmailChange(newValue: String) {
        uiState.value = uiState.value.copy(email = newValue)
    }

    fun onPasswordChange(newValue: String) {
        uiState.value = uiState.value.copy(password = newValue)
    }

    fun onRepeatPasswordChange(newValue: String) {
        uiState.value = uiState.value.copy(repeatPassword = newValue)
    }

    fun onSignUpClick() {
        viewModelScope.launch {
            createAccountUseCase.execute(
                email = uiState.value.email,
                password = uiState.value.password,
                repeatPassword = uiState.value.repeatPassword
            )
                .onSuccess {
                    _effect.send(SignUpContract.Effect.NavigateToSignIn)
                }
                .onFailure {
                    _effect.send(SignUpContract.Effect.ShowErrorMessage(it.message))
                }
        }
    }
}


