package com.jurassic.myhealth.login.presentation.feature.signup.composable

import androidx.compose.material.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.navigation.NavController
import com.jurassic.myhealth.login.presentation.feature.signin.composable.SIDE_EFFECTS_KEY
import com.jurassic.myhealth.login.presentation.feature.signup.model.SignUpContract
import com.jurassic.myhealth.login.presentation.feature.signup.viewmodel.SignUpViewModel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun SignUpScreen(
  navController: NavController
) {
  val viewModel = koinViewModel<SignUpViewModel>()
  val snackbar = remember { SnackbarHostState() }

  val uiState by viewModel.uiState

  LaunchedEffect(SIDE_EFFECTS_KEY) {
    viewModel.effect.onEach { effect ->
      when (effect) {
        SignUpContract.Effect.NavigateToSignIn -> navController.navigateUp()
        is SignUpContract.Effect.ShowErrorMessage -> snackbar.showSnackbar(
          message = effect.message ?: "lol",
        )
      }
    }.collect()
  }

  SignUpScreenContent(
    uiState = uiState,
    onEmailChange = viewModel::onEmailChange,
    onPasswordChange = viewModel::onPasswordChange,
    onRepeatPasswordChange = viewModel::onRepeatPasswordChange,
    onSignUpClick = { viewModel.onSignUpClick() }
  )
}