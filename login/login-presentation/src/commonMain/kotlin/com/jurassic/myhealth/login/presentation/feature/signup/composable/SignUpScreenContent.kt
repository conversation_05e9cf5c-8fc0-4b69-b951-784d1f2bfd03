package com.jurassic.myhealth.login.presentation.feature.signup.composable

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.jurassic.myhealth.login.presentation.common.composable.BasicButton
import com.jurassic.myhealth.login.presentation.common.composable.EmailField
import com.jurassic.myhealth.login.presentation.common.composable.PasswordField
import com.jurassic.myhealth.login.presentation.common.composable.RepeatPasswordField
import com.jurassic.myhealth.login.presentation.common.ext.basicButton
import com.jurassic.myhealth.login.presentation.common.ext.fieldModifier
import com.jurassic.myhealth.login.presentation.feature.signup.model.SignUpContract
import myhealth.login.login_presentation.generated.resources.Res
import myhealth.login.login_presentation.generated.resources.create_account
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
fun SignUpScreenContent(
    modifier: Modifier = Modifier,
    uiState: SignUpContract.UiState,
    onEmailChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onRepeatPasswordChange: (String) -> Unit,
    onSignUpClick: () -> Unit
) {
    val fieldModifier = Modifier.fieldModifier()

    Column(
        modifier = modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        EmailField(uiState.email, onEmailChange, fieldModifier)
        PasswordField(uiState.password, onPasswordChange, fieldModifier)
        RepeatPasswordField(uiState.repeatPassword, onRepeatPasswordChange, fieldModifier)

        BasicButton(Res.string.create_account, Modifier.basicButton()) {
            onSignUpClick()
        }
    }
}

@Preview
@Composable
fun SignUpScreenPreview() {
    val uiState = SignUpContract.UiState(
        email = "<EMAIL>"
    )

    MaterialTheme {
        SignUpScreenContent(
            uiState = uiState,
            onEmailChange = { },
            onPasswordChange = { },
            onRepeatPasswordChange = { },
            onSignUpClick = { }
        )
    }
}
