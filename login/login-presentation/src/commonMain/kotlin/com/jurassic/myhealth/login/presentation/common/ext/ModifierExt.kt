package com.jurassic.myhealth.login.presentation.common.ext

import androidx.compose.foundation.layout.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

fun Modifier.textButton(): Modifier =
  this.fillMaxWidth().padding(16.dp, 8.dp, 16.dp, 0.dp)

fun Modifier.basicButton(): Modifier =
  this.fillMaxWidth().padding(16.dp, 8.dp)

fun Modifier.card(): Modifier =
  this.padding(16.dp, 0.dp, 16.dp, 8.dp)

fun Modifier.contextMenu(): Modifier =
  this.wrapContentWidth()

fun Modifier.alertDialog(): Modifier =
  this.wrapContentWidth().wrapContentHeight()

fun Modifier.dropdownSelector(): Modifier =
  this.fillMaxWidth()

fun Modifier.fieldModifier(): Modifier =
  this.fillMaxWidth().padding(16.dp, 4.dp)

fun Modifier.toolbarActions(): Modifier =
  this.wrapContentSize(Alignment.TopEnd)

fun Modifier.spacer(): Modifier =
  this.fillMaxWidth().padding(12.dp)

fun Modifier.smallSpacer(): Modifier =
  this.fillMaxWidth().height(8.dp)
