package com.jurassic.myhealth.login.presentation.feature.signin.composable

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.jurassic.myhealth.login.presentation.common.composable.BasicButton
import com.jurassic.myhealth.login.presentation.common.composable.BasicTextButton
import com.jurassic.myhealth.login.presentation.common.composable.EmailField
import com.jurassic.myhealth.login.presentation.common.composable.PasswordField
import com.jurassic.myhealth.login.presentation.common.ext.basicButton
import com.jurassic.myhealth.login.presentation.common.ext.fieldModifier
import com.jurassic.myhealth.login.presentation.common.ext.textButton
import com.jurassic.myhealth.login.presentation.feature.signin.model.SignInContract
import myhealth.login.login_presentation.generated.resources.Res
import myhealth.login.login_presentation.generated.resources.create_account
import myhealth.login.login_presentation.generated.resources.forgot_password
import myhealth.login.login_presentation.generated.resources.sign_in
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
internal fun SignInScreenContent(
    modifier: Modifier = Modifier,
    uiState: SignInContract.UiState,
    onEmailChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onSignInClick: () -> Unit,
    onForgotPasswordClick: () -> Unit,
    onCreateAccountClick: () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        EmailField(uiState.email, onEmailChange, Modifier.fieldModifier())
        PasswordField(uiState.password, onPasswordChange, Modifier.fieldModifier())

        BasicButton(Res.string.sign_in, Modifier.basicButton()) { onSignInClick() }

        BasicTextButton(Res.string.forgot_password, Modifier.textButton()) {
            onForgotPasswordClick()
        }
        BasicTextButton(Res.string.create_account, Modifier.textButton()) {
            onCreateAccountClick()
        }
    }
}

@Preview
@Composable
fun LoginScreenPreview() {
    val uiState = SignInContract.UiState(
        email = "<EMAIL>"
    )

    MaterialTheme {
        SignInScreenContent(
            uiState = uiState,
            onEmailChange = { },
            onPasswordChange = { },
            onSignInClick = { },
            onForgotPasswordClick = { },
            onCreateAccountClick = { },
        )
    }
}