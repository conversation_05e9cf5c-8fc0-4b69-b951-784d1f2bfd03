package com.jurassic.myhealth.home.presentation.feature.provider.feature.add.composable

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.navigation.NavController
import com.jurassic.myhealth.home.presentation.feature.provider.feature.add.model.AddProviderContract
import com.jurassic.myhealth.home.presentation.feature.provider.feature.add.viewmodel.AddProviderViewModel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun AddProviderScreen(
    navController: NavController
) {
    val viewModel = koinViewModel<AddProviderViewModel>()

    LaunchedEffect(SIDE_EFFECTS_KEY) {
        viewModel.effect.onEach { effect ->
            when(effect) {
                AddProviderContract.Effect.NavigateToHome -> TODO()
                AddProviderContract.Effect.NavigateToLoginProvider -> navController.navigate("login_provider")
            }
        }.collect()
    }

    AddProviderScreenContent(
        uiState = viewModel.uiState.value,
        onLuxmedClick = viewModel::onLuxmedClick,
    )
}

const val SIDE_EFFECTS_KEY = "side-effects_key"