package com.jurassic.myhealth.home.presentation.feature.provider.feature.add.composable

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Button
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.sp
import com.jurassic.myhealth.home.presentation.feature.provider.feature.add.model.AddProviderContract
import myhealth.home.home_presentation.generated.resources.Res
import myhealth.home.home_presentation.generated.resources.luxmed
import org.jetbrains.compose.resources.stringResource

@Composable
fun AddProviderScreenContent(
    modifier: Modifier = Modifier,
    uiState: AddProviderContract.UiState,
    onLuxmedClick: () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Button(
            onClick = { onLuxmedClick() },
            modifier = modifier,
        ) {
            Text(text = stringResource(Res.string.luxmed), fontSize = 16.sp)
        }
    }
}