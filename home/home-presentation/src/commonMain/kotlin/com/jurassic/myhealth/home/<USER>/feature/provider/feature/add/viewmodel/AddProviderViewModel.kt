package com.jurassic.myhealth.home.presentation.feature.provider.feature.add.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import com.jurassic.myhealth.home.presentation.feature.provider.feature.add.model.AddProviderContract
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow

class AddProviderViewModel : ViewModel() {

    private val _effect: Channel<AddProviderContract.Effect> = Channel()
    val effect = _effect.receiveAsFlow()

    var uiState = mutableStateOf(AddProviderContract.UiState())
        private set

    fun onLuxmedClick() {
        _effect.trySend(AddProviderContract.Effect.NavigateToLoginProvider)
    }
}