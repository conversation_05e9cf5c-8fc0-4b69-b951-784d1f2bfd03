package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.composable

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LoginProviderContract
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LuxMedAuthentication
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.SyncState

@Composable
fun LoginProviderScreenContent(
    modifier: Modifier = Modifier,
    uiState: LoginProviderContract.UiState,
    onTokenReceived: (LuxMedAuthentication) -> Unit
) {
    Box(modifier = modifier.fillMaxSize()) {
        when (uiState.syncState) {
            is SyncState.Loading -> {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        CircularProgressIndicator()
                        Text(
                            text = "Synchronizing LuxMed data...",
                            style = MaterialTheme.typography.body1,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }
            is SyncState.Success -> {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "✅ ${uiState.syncState.message}",
                        style = MaterialTheme.typography.body1,
                        color = Color.Green
                    )
                }
            }
            is SyncState.Error -> {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "❌ ${uiState.syncState.message}",
                        style = MaterialTheme.typography.body1,
                        color = Color.Red
                    )
                }
            }
            SyncState.Idle -> {
                Column {
                    // TODO change to dynamic load from any provider
                    MyHealthWebView(
                        url = "https://portalpacjenta.luxmed.pl/PatientPortal/NewPortal/Page/Account/Login",
                        onAuthenticationTokenReceived = { onTokenReceived(it) }
                    )
                }
            }
        }
    }
}