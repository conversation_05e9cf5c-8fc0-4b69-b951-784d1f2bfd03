package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model

class LoginProviderContract {

    data class UiState(
        val email: String = "",
        val password: String = "",
        val syncState: SyncState = SyncState.Idle
    )

    sealed class Effect  {
        data object NavigateToHome : Effect()
        data class ShowMessage(val message: String) : Effect()
    }
}