package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.mapper

import com.jurassic.myhealth.home.domain.model.LuxmedAuthData
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LuxMedAuthentication

class LuxmedAuthMapper {

    fun toDomain(luxMedAuthentication: LuxMedAuthentication): LuxmedAuthData =
        LuxmedAuthData(
            jwtToken = luxMedAuthentication.jwtToken,
            aspNetSessionId = luxMedAuthentication.aspNetSessionId,
            lxToken = luxMedAuthentication.lxToken,
            refreshToken = luxMedAuthentication.refreshToken,
            userAdditionalInfo = luxMedAuthentication.userAdditionalInfo,
            xsrfToken = luxMedAuthentication.xsrfToken,
            incapsulaSessionId = luxMedAuthentication.incapsulaSessionId,
            deviceId = luxMedAuthentication.deviceId
        )
}
