package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.di

import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.mapper.LuxmedAuthMapper
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.viewmodel.LoginProviderViewModel
import org.koin.compose.viewmodel.dsl.viewModelOf
import org.koin.core.module.dsl.factoryOf
import org.koin.dsl.module

val loginProviderModule = module {
    factoryOf(::LuxmedAuthMapper)

    viewModelOf(::LoginProviderViewModel)
}