package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.composable

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.interop.UIKitView
import androidx.compose.ui.unit.dp
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LuxMedAuthentication
import kotlinx.cinterop.CValue
import kotlinx.cinterop.ExperimentalForeignApi
import platform.CoreGraphics.CGRect
import platform.Foundation.NSURL
import platform.QuartzCore.CATransaction
import platform.QuartzCore.kCATransactionDisableActions
import platform.UIKit.UIView
import platform.WebKit.WKWebView
import platform.WebKit.WKWebViewConfiguration

@OptIn(ExperimentalForeignApi::class)
@Composable
actual fun MyHealthWebView(
    url: String,
    onAuthenticationTokenReceived: (LuxMedAuthentication) -> Unit,
) {
    val webView = remember { WKWebView() }

    UIKitView(
        modifier = Modifier.fillMaxWidth().padding(top = 12.dp),
        factory = {
            val container = UIView()
            webView.apply {
                WKWebViewConfiguration().apply {
                    allowsInlineMediaPlayback = true
                    allowsAirPlayForMediaPlayback = true
                    allowsPictureInPictureMediaPlayback = true
                }
                loadHTMLString("", baseURL = NSURL.URLWithString(url))
            }
            container.addSubview(webView)
            container
        },
        onResize = { view: UIView, rect: CValue<CGRect> ->
            CATransaction.begin()
            CATransaction.setValue(true, kCATransactionDisableActions)
            view.layer.setFrame(rect)
            webView.setFrame(rect)
            CATransaction.commit()
        })
}