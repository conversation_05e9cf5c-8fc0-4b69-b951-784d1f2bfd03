package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.composable

import android.view.View
import android.view.ViewGroup
import android.webkit.CookieManager
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LuxMedAuthentication


@Composable
actual fun MyHealthWebView(
    url: String,
    onAuthenticationTokenReceived: (LuxMedAuthentication) -> Unit,
) {
    Box(
        modifier = Modifier.fillMaxWidth().verticalScroll(rememberScrollState())
    ) {
        AndroidView(
            modifier = Modifier.fillMaxWidth(),
            factory = {
                WebView(it).apply {
                    scrollBarStyle = View.SCROLLBARS_OUTSIDE_OVERLAY
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )

                    settings.apply {
                        javaScriptEnabled = true
                        loadsImagesAutomatically = true
                        allowContentAccess = true
                        useWideViewPort = true
                        loadWithOverviewMode = true
                        domStorageEnabled = true
                        databaseEnabled = true
                    }

                    webChromeClient = WebChromeClient()
                    webViewClient = object : WebViewClient() {
                        override fun shouldInterceptRequest(
                            view: WebView,
                            request: WebResourceRequest
                        ): WebResourceResponse? {
                            val requestUrl = request.url.toString()

                            if (requestUrl == "https://portalpacjenta.luxmed.pl/PatientPortal/NewPortal/Inbox/GetMenu") {
                                val luxMedAuthentication = request.toLuxMedAuthentication()
                                val response = super.shouldInterceptRequest(view, request)
                                onAuthenticationTokenReceived(luxMedAuthentication)
                                return response
                            }

                            return super.shouldInterceptRequest(view, request)
                        }
                    }
                }
            },
            update = { webView ->
                webView.loadUrl(url)
            }
        )
    }
}

fun WebResourceRequest.toLuxMedAuthentication(): LuxMedAuthentication {
    val token = requestHeaders["Authorization-Token"] ?: ""

    val url = url.toString()
    val cookies = CookieManager.getInstance().getCookie(url) ?: ""

    return LuxMedAuthentication(
        jwtToken = token,
        aspNetSessionId = getCookieValue(cookies, "ASP.NET_SessionId"),
        lxToken = getCookieValue(cookies, "LXToken"),
        refreshToken = getCookieValue(cookies, "RefreshToken"),
        userAdditionalInfo = getCookieValue(cookies, "UserAdditionalInfo"),
        xsrfToken = findCookieByPrefix(cookies, "__RequestVerificationToken_"),
        incapsulaSessionId = findCookieByPrefix(cookies, "incap_ses_"),
        deviceId = getCookieValue(cookies, "PatientPortalDeviceId")
    )
}

fun getCookieValue(cookies: String, key: String): String =
    cookies.split(';')
        .firstOrNull { it.trim().startsWith("$key=") }
        ?.substringAfter("=")
        ?.trim() ?: ""

fun findCookieByPrefix(cookies: String, prefix: String): String =
    cookies.split(';')
        .firstOrNull { it.trim().startsWith(prefix) }
        ?.substringAfter("=")
        ?.trim() ?: ""

