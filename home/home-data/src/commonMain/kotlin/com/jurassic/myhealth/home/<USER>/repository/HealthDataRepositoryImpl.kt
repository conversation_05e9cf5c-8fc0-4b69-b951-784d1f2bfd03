package com.jurassic.myhealth.home.data.repository

import com.jurassic.myhealth.home.data.api.MyHealthApiService
import com.jurassic.myhealth.home.data.model.LuxmedSyncRequest
import com.jurassic.myhealth.home.data.provider.AuthenticationProvider
import com.jurassic.myhealth.home.domain.model.LuxmedAuthData
import com.jurassic.myhealth.home.domain.repository.HealthDataRepository

class HealthDataRepositoryImpl(
    private val apiService: MyHealthApiService,
    private val authenticationProvider: AuthenticationProvider
) : HealthDataRepository {

    override suspend fun syncLuxmedData(luxmedAuth: LuxmedAuthData): Result<Unit> {
        val authToken = authenticationProvider.getMyHealthToken()

        val request = LuxmedSyncRequest(
            jwtToken = luxmedAuth.jwtToken,
            aspNetSessionId = luxmedAuth.aspNetSessionId,
            lxToken = luxmedAuth.lxToken,
            refreshToken = luxmedAuth.refreshToken,
            userAdditionalInfo = luxmedAuth.userAdditionalInfo,
            xsrfToken = luxmedAuth.xsrfToken,
            incapsulaSessionId = luxmedAuth.incapsulaSessionId,
            deviceId = luxmedAuth.deviceId
        )

        return apiService.syncLuxmedData("Bearer $authToken", request)
            .fold(
                onSuccess = { response ->
                    if (response.success) {
                        Result.success(Unit)
                    } else {
                        Result.failure(Exception(response.message))
                    }
                },
                onFailure = { error ->
                    Result.failure(error)
                }
            )
    }
}
