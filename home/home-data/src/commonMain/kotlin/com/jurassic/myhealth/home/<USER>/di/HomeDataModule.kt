package com.jurassic.myhealth.home.data.di

import com.jurassic.myhealth.home.data.api.MyHealthApiService
import com.jurassic.myhealth.home.data.api.MyHealthApiServiceImpl
import com.jurassic.myhealth.home.data.network.HttpClientFactory
import com.jurassic.myhealth.home.data.repository.HealthDataRepositoryImpl
import com.jurassic.myhealth.home.domain.repository.HealthDataRepository
import io.ktor.client.HttpClient
import org.koin.core.module.dsl.factoryOf
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module

val homeDataModule = module {

    single<HttpClient> { HttpClientFactory.create() }

    factoryOf(::MyHealthApiServiceImpl) bind MyHealthApiService::class

    factoryOf(::HealthDataRepositoryImpl) bind HealthDataRepository::class
}
